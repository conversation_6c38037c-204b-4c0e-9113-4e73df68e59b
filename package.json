{"name": "cursor-pool", "private": true, "version": "1.8.13", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri", "generate-icons": "tauricon", "lint:fix": "eslint --ext .js,.ts,.vue src --fix", "format": "prettier --write src/", "format:check": "prettier --check src/", "prepare": "husky", "lint-staged": "lint-staged"}, "dependencies": {"@tauri-apps/api": "^2.2.0", "@tauri-apps/plugin-dialog": "^2.2.0", "@tauri-apps/plugin-notification": "~2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-os": "^2.2.0", "@tauri-apps/plugin-positioner": "^2.2.0", "@tauri-apps/plugin-process": "^2.2.0", "@tauri-apps/plugin-shell": "^2.2.0", "@tauri-apps/plugin-updater": "^2.6.0", "@types/crypto-js": "^4.2.2", "@types/uuid": "^10.0.0", "crypto-js": "^4.2.0", "driver.js": "^1.3.5", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0", "naive-ui": "^2.41.0", "pinia": "^3.0.1", "tauri-plugin-macos-permissions-api": "^2.2.0", "uuid": "^11.0.5", "vfonts": "^0.0.3", "vue": "^3.5.13", "vue-router": "^4.2.5"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@tauri-apps/cli": "^2", "@tauri-apps/tauricon": "^1.0.3", "@types/markdown-it": "^14.1.2", "@types/node": "^22.13.2", "@unocss/preset-attributify": "0.58.0", "@unocss/preset-icons": "0.58.0", "@unocss/preset-uno": "0.58.0", "@unocss/transformer-directives": "0.58.0", "@vicons/antd": "^0.13.0", "@vicons/ionicons5": "^0.12.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.21.1", "husky": "^9.1.7", "lint-staged": "^15.5.0", "prettier": "^3.2.5", "sharp": "^0.34.1", "typescript": "~5.6.2", "unocss": "0.58.0", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}, "lint-staged": {"*.{js,ts,vue}": ["eslint --fix", "prettier --write"]}}