name: Dev Build

on:
  workflow_dispatch:
  push:
    branches:
      - main
      - master
      - dev
    paths-ignore:
      - '**.md'
      - '.github/workflows/release.yml'
      - '.github/workflows/updater.yml'

jobs:
  check-version:
    runs-on: ubuntu-latest
    outputs:
      should_build: ${{ steps.check.outputs.should_build }}
      version: ${{ steps.check.outputs.version }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
      
      - name: Check if package.json version changed
        id: check
        run: |
          git diff HEAD^ HEAD --name-only | grep package.json || echo "No package.json changes"
          
          # 获取当前版本
          CURRENT_VERSION=$(node -p "require('./package.json').version")
          echo "Current version: $CURRENT_VERSION"
          echo "version=v$CURRENT_VERSION" >> $GITHUB_OUTPUT
          
          # 手动触发
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "Workflow manually triggered, building..."
            echo "should_build=true" >> $GITHUB_OUTPUT
            exit 0
          fi
          
          # 检查package.json中version是否改变
          if git diff HEAD^ HEAD --name-only | grep -q package.json; then
            PREV_VERSION=$(git show HEAD^:package.json | node -p "JSON.parse(require('fs').readFileSync(0)).version")
            echo "Previous version: $PREV_VERSION"
            
            if [[ "$CURRENT_VERSION" != "$PREV_VERSION" ]]; then
              echo "Version changed, building..."
              echo "should_build=true" >> $GITHUB_OUTPUT
            else
              echo "Version not changed, skipping build."
              echo "should_build=false" >> $GITHUB_OUTPUT
            fi
          else
            echo "Should build anyway..."
            echo "should_build=true" >> $GITHUB_OUTPUT
          fi

  build-tauri:
    needs: check-version
    if: needs.check-version.outputs.should_build == 'true'
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: 'macos-14'
            args: '--target aarch64-apple-darwin'
            display_name: 'macOS-ARM64'
          - platform: 'macos-latest'
            args: '--target x86_64-apple-darwin'
            display_name: 'macOS-Intel'
          - platform: 'windows-latest'
            args: '--target x86_64-pc-windows-msvc'
            display_name: 'Windows-x64'
          - platform: 'windows-latest'
            args: '--target aarch64-pc-windows-msvc'
            display_name: 'Windows-ARM64'

    runs-on: ${{ matrix.platform }}
    name: Dev Build - ${{ matrix.display_name }}
    steps:
      - uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: latest
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'

      - name: install Rust stable
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.platform == 'macos-latest' && 'x86_64-apple-darwin' || matrix.platform == 'macos-14' && 'aarch64-apple-darwin' || contains(matrix.args, 'aarch64-pc-windows-msvc') && 'aarch64-pc-windows-msvc' || '' }}

      - name: Install dependencies (macOS)
        if: matrix.platform == 'macos-latest' || matrix.platform == 'macos-14'
        run: |
          brew install openssl@3

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          workspaces: './src-tauri -> target'

      - name: Install dependencies
        run: pnpm install

      - name: Build Tauri App (macOS)
        if: matrix.platform == 'macos-latest' || matrix.platform == 'macos-14'
        env:
          TAURI_SIGNING_PRIVATE_KEY: ${{ secrets.TAURI_SIGNING_PRIVATE_KEY }}
          TAURI_SIGNING_PRIVATE_KEY_PASSWORD: ${{ secrets.TAURI_SIGNING_PRIVATE_KEY_PASSWORD }}
        run: |
          pnpm tauri build ${{ matrix.args }}

      - name: Build Tauri App (Windows)
        if: matrix.platform == 'windows-latest'
        env:
          TAURI_SIGNING_PRIVATE_KEY: ${{ secrets.TAURI_SIGNING_PRIVATE_KEY }}
          TAURI_SIGNING_PRIVATE_KEY_PASSWORD: ${{ secrets.TAURI_SIGNING_PRIVATE_KEY_PASSWORD }}
        run: |
          pnpm tauri build ${{ matrix.args }}

      - name: Upload Dev Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: dev-${{ needs.check-version.outputs.version }}-${{ matrix.display_name }}
          path: |
            src-tauri/target/*/release/bundle/msi/*.msi
            src-tauri/target/*/release/bundle/nsis/*.exe
            src-tauri/target/*/release/bundle/dmg/*.dmg
            src-tauri/target/*/release/bundle/deb/*.deb
            src-tauri/target/*/release/bundle/appimage/*.AppImage
            src-tauri/target/*/release/bundle/macos/*.app
          retention-days: 7 