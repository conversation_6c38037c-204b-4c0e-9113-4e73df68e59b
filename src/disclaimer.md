# 软件免责声明

**发布日期：2025年03月14日**  
**生效条件**：用户安装、下载或使用本软件即视为同意本声明全部条款

---

## 一、权利声明

### 1. 知识产权归属

- 本软件的一切著作权、商标权、专利权等知识产权，包括但不限于文字、图标、图像、界面设计、代码及文档，均归开发者（以下简称“我方”）所有，受《中华人民共和国著作权法》《计算机软件保护条例》等法律法规保护。
- Cursor Pool 客户端为免费开源软件，源代码所有权及修改权归原始开发者所有。第三方衍生版本必须完整保留开源声明及作者标识。
- 禁止任何反向工程、反编译、反汇编或拆分软件组件的行为，商业使用需书面授权。

### 2. 使用许可

- 用户获得非独占、不可转让的免费使用许可，仅限于非商业用途。
- 禁止将软件用于传播非法信息、破坏计算机系统等违法活动。

### 3. 操作授权

用户同意授权本软件执行以下操作：

- 对 Cursor 客户端主执行文件进行 hook 注入
- 修改 Cursor 本地生成文件的权限及内容
- 变更 Cursor 客户端机器码标识

---

## 二、用户义务

### 1. 合法使用

用户承诺不利用本软件实施以下行为：

- 删除或篡改软件权利管理信息
- 绕过技术保护措施
- 传播病毒、破坏数据或干扰网络
- 进行任何违法违规活动，产生一切后果由用户本人承担

### 2. 数据责任

- 用户需自行备份重要数据，因使用本软件导致的数据丢失、泄露或设备损坏，我方不承担责任

---

## 三、免责范围

### 1. 软件风险

因系统兼容性、软件冲突导致的以下问题，开发者不承担责任：

- Cursor 客户端功能异常或数据丢失
- 账号刷新失败或服务中断
- 机器码修改引发的账号封禁风险

### 2. 第三方内容

- 软件内链接的第三方服务、下载内容或广告的合法性由提供方负责，用户需自行判断并承担使用风险
- 本软件不涉及 Cursor 官方服务，因使用本工具导致的账号权益损失（如功能限制、封号等），由用户自行承担后果

### 3. 不可抗力

- 因自然灾害、政策调整、网络故障、病毒攻击、Cursor 官方问题等不可控因素导致的问题，我方免责

---

## 四、隐私与安全

### 1. 数据收集

- 软件运行可能向服务器发送设备标识符以统计使用次数，该数据不与用户身份信息关联

### 2. 安全声明

- 本软件不含监控用户行为、窃取隐私的恶意代码，但无法保证第三方组件的行为

---

## 五、争议解决

### 1. 法律适用

- 本声明适用中华人民共和国法律，争议协商不成时提交北京仲裁委员会仲裁，裁决为终局

### 2. 条款更新

- 我方保留修改声明的权利，修改内容发布于官方网站，用户继续使用视为接受变更

---

**联系方式**  
侵权投诉或技术支持请联络：[<EMAIL>](mailto:<EMAIL>)

**附则**

- 本声明参考《计算机软件保护条例》第十七条，用户可基于学习研究目的合理使用软件功能。
- 声明解释权归开发者所有
