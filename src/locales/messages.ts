export const messages = {
  'zh-CN': {
    appName: 'Cursor Pool',
    menu: {
      dashboard: '概览',
      historyAccount: '历史账户',
      history: '操作记录',
      settings: '设置',
    },
    dashboard: {
      deviceInfo: '设备信息',
      machineCode: '机器码',
      currentAccount: '当前账户',
      expiryDate: '会员到期时间',
      usageStats: '使用统计',
      advancedModel: '高级模型使用量',
      normalModel: '普通模型使用量',
      quickActions: '快捷操作',
      changeAccount: '更换账户',
      changeMachineCode: '更换机器码',
      quickChange: '一键更换(账户+机器码)',
      changeSuccess: '更换成功',
      changeFailed: '更换失败',
      accountChangeSuccess: '账户更换成功',
      accountChangeFailed: '账户更换失败',
      machineChangeSuccess: '机器码更换成功',
      machineChangeFailed: '机器码更换失败',
      userInfo: '用户信息',
      level: '会员等级',
      username: 'CP账户邮箱',
      expireTime: '到期时间',
      dailyUsage: '每日使用量',
      cursorInfo: 'Cursor 信息',
      cursorAccount: 'Cursor 账户',
      cursorUsage: 'Cursor 使用量',
      notLoggedIn: '未登录',
      unlimited: '无限制',
      serverNotConnected: '未连接服务器',
      codeUnused: '未使用',
      codeExpired: '已过期',
      codeRefunded: '已退款',
      codeEnded: '已结束',
      memberLevel: {
        1: '码农',
        2: '程序员',
        3: '工程师',
        4: '架构师',
        5: '技术总监',
      },
      newVersionAvailable: '发现新版本',
      currentVersion: '当前版本',
      newVersion: '最新版本',
      later: '稍后更新',
      downloadNow: '立即下载',
      unusedCreditsWarning: '使用提醒',
      unusedCreditsMessage: '您还有 {count} 次高级模型使用次数未使用, 确定要切换账号吗？',
      confirmSwitch: '确认切换',
      cancelSwitch: '取消',
      ccStatus: '本地Curso状态',
      registerTime: '本地Cursor账户注册时间',
      insufficientCredits: '剩余额度不足, 请先充值',
      email: '本地Cursor邮箱',
      cpUsage: 'Cursor Pool额度使用量',
      advancedModelUsage: '高级模型使用量',
      basicModelUsage: '普通模型使用量',
      cannotGetUsage: '无法链接cursor服务器, 请重启软件',
      cursorHistoryDownload: 'Cursor 历史版本下载',
      cursorDbError: 'Cursor未安装或从未启动过',
      cursorNetworkError: '网络连接错误, 无法链接cursor服务器',
      cursorDataError: 'Cursor服务器返回数据格式异常',
      cursorUnknownError: 'Cursor未知错误, 请重启软件',
      cursorProUnlimitedTip: 'Cursor最新政策改为Pro以及Pro试用用户可以无限制调用模型',
    },
    login: {
      title: '登录',
      emailPlaceholder: '请输入邮箱',
      passwordPlaceholder: '请输入密码',
      smsCodePlaceholder: '请输入验证码',
      sendCode: '发送验证码',
      resendCode: '{seconds}秒后重新发送',
      loginButton: '登录',
      registerButton: '注册',
      forgotPassword: '忘记密码？',
      resetPassword: '重置密码',
      emailError: '请输入有效的邮箱地址',
      passwordError: '请输入密码',
      loginSuccess: '登录成功',
      loginFailed: '登录失败',
      noAccount: '还没有账户？',
      register: '立即注册',
      hasAccount: '已有账户？去登录',
      userExists: '该邮箱已注册, 已切换到登录模式',
      userNotExists: '该邮箱未注册, 请先注册账户',
      emailInvalid: '请输入有效的邮箱地址',
      emailUnsupported: '暂不支持该邮箱域名',
      passwordInvalid: '密码必须包含至少8个字符, 包括大小写字母和数字',
    },
    settings: {
      activation: '激活码兑换',
      activationCode: '激活码',
      activate: '激活',
      changePassword: '修改密码',
      currentPassword: '当前密码',
      newPassword: '新密码',
      confirmPassword: '确认新密码',
      about: '关于',
      globalPreferences: '全局偏好设置',
      closeMethod: '关闭方式',
      operationMode: '操作模式',
      simpleMode: '简单模式',
      advancedMode: '高级模式',
      switchedToAdvanced: '已切换到高级模式',
      switchedToSimple: '已切换到简单模式',
      settingsFailed: '设置失败',
      route: '线路',
    },
    history: {
      title: '操作记录',
      filter: '筛选',
      dateRange: '选择日期范围',
      type: '操作类型',
      detail: '详情',
      time: '时间',
      operator: '操作者',
      datePlaceholder: '选择日期范围',
      clearHistory: '清除历史记录',
      clearSuccess: '历史记录已清除',
      clearFailed: '清除历史记录失败',
    },
    message: {
      pleaseInputActivationCode: '请输入激活码',
      activationSuccess: '激活成功',
      activationFailed: '激活失败',
      pleaseInputEmail: '请输入账户邮箱',
      addSuccess: '添加成功',
      switchSuccess: '切换到账户: {email}',
      deleteSuccess: '删除成功',
      pleaseInputPassword: '请填写完整密码信息',
      passwordNotMatch: '两次输入的新密码不一致',
      passwordChangeSuccess: '密码修改成功',
      passwordChangeFailed: '密码修改失败',
    },
    systemControl: {
      title: '系统控制',
      hookStatus: '客户端注入状态',
      hookApplied: '已注入客户端',
      hookNotApplied: '未注入或客户端不支持',
      applyHook: '注入客户端',
      restoreHook: '还原客户端',
      clientStatus: '客户端状态',
      clientVerified: '已验证',
      clientUnverified: '未验证',
      cursorPath: 'Cursor路径: ',
      changePath: '修改路径',
      systemNotification: '系统通知',
      authorized: '已授权',
      unauthorized: '未授权',
      requestPermission: '请求权限',
      messages: {
        applyHookSuccess: '注入客户端成功',
        restoreHookSuccess: '恢复客户端成功',
        cursorRunning: 'Cursor进程正在运行, 请先关闭Cursor',
        forceKillConfirm: '我已保存, 强制关闭',
        permissionGranted: '通知权限已授予',
        permissionDenied: '通知权限被拒绝，请在系统设置中手动启用',
      },
      history: {
        applyHook: '注入客户端',
        restoreHook: '恢复客户端',
      },
    },
    about: {
      title: '关于',
      version: '版本',
      appName: 'Cursor Pool',
      copyright: '版权所有',
      license: '基于 MIT 协议开源，修改和分发时需保留版权信息',
      allRightsReserved: '保留所有权利',
    },
    language: {
      title: '语言设置',
      switch: '切换语言',
    },
    common: {
      logout: '登出账户',
      copySuccess: '复制成功',
      copyFailed: '复制失败',
      forceClose: '我已保存, 强制关闭',
      cursorRunning: 'Cursor 正在运行',
      cursorRunningMessage:
        '检测到 Cursor 正在运行, 请保存尚未更改的项目再继续操作! 不保存会导致Cursor报错! 报错了请别联系客服!',
      closingCursor: '正在关闭 Cursor...',
      forgotPassword: '忘记密码？',
      unknown: '未知',
      notBound: '本地Cursor没有登录账户',
      verified: '已验证',
      unverified: '未验证',
      success: '成功',
      failed: '失败',
      loading: '加载中...',
      confirmRestart: '确定重启',
      timeUnknown: '未知',
      timeExpired: '已过期',
      timeDays: '天',
      timeHours: '小时',
      timeMinutes: '分钟',
      statusUnknown: '未知状态',
    },
    inbound: {
      title: '线路',
      selector: '选择线路',
      switchSuccess: '已切换到线路: {name}',
      switchFailed: '切换线路失败',
      restartNeeded: '为确保新线路配置生效，建议重启应用',
      defaultInbound: '默认线路',
      domestic: '国内线路',
      foreign: '国外线路',
    },
    closeType: {
      ask: '每次询问',
      minimize: '最小化',
      exit: '退出程序',
    },
    historyAccount: {
      title: '历史账户',
      email: '邮箱',
      machineCode: '机器码',
      advancedModelUsage: '高级模型使用量',
      actions: '操作',
      switchButton: '切换',
      deleteButton: '删除',
      refreshAll: '刷新所有账户',
      clearHighUsage: '清理高使用量账户',
      switchSuccess: '切换账户成功',
      switchFailed: '切换账户失败',
      deleteSuccess: '删除成功',
      deleteFailed: '删除失败',
      hookFailed: '注入失败，请手动注入后再试',
      refreshSuccess: '所有账户刷新成功',
      refreshPartial: '成功刷新 {success}/{total} 个账户',
      refreshFailed: '刷新使用情况失败',
      noHighUsageAccounts: '没有高使用量账户需要清理',
      clearHighUsageSuccess: '成功清理 {count} 个高使用量账户',
      clearHighUsageFailed: '清理高使用量账户失败',
      loadFailed: '加载历史账户失败',
    },
    notification: {
      testTitle: '通知测试',
      testBody: '恭喜！通知功能已成功启用。',
    },
    network: {
      retrying: '正在重试 {endpoint}，第 {attempt}/{max} 次',
      refreshingInbound: '正在刷新线路...',
      inboundRefreshed: '线路刷新完成',
      requestFailed: '请求失败，正在尝试重新连接',
      connectionRestored: '连接已恢复',
    },
  },
  'en-US': {
    appName: 'Cursor Pool',
    menu: {
      dashboard: 'Dashboard',
      historyAccount: 'Account History',
      history: 'History',
      settings: 'Settings',
    },
    dashboard: {
      deviceInfo: 'Device Info',
      machineCode: 'Machine Code',
      currentAccount: 'Current Account',
      expiryDate: 'Expiry Date',
      usageStats: 'Usage Statistics',
      advancedModel: 'Advanced Model Usage',
      normalModel: 'Normal Model Usage',
      quickActions: 'Quick Actions',
      changeAccount: 'Change Account',
      changeMachineCode: 'Change Machine Code',
      quickChange: 'Quick Change (Account + Machine Code)',
      changeSuccess: 'Change Success',
      changeFailed: 'Change Failed',
      accountChangeSuccess: 'Account Change Success',
      accountChangeFailed: 'Account Change Failed',
      machineChangeSuccess: 'Machine Change Success',
      machineChangeFailed: 'Machine Change Failed',
      userInfo: 'User Information',
      level: 'Member Level',
      username: 'Username',
      expireTime: 'Expiry Time',
      dailyUsage: 'Daily Usage',
      cursorInfo: 'Cursor Info',
      cursorAccount: 'Cursor Account',
      cursorUsage: 'Cursor Usage',
      notLoggedIn: 'Not Logged In',
      unlimited: 'Unlimited',
      serverNotConnected: 'Server Not Connected',
      codeUnused: 'Unused',
      codeExpired: 'Expired',
      codeRefunded: 'Refunded',
      codeEnded: 'Ended',
      memberLevel: {
        1: 'Coder',
        2: 'Programmer',
        3: 'Engineer',
        4: 'Architect',
        5: 'Technical Director',
      },
      newVersionAvailable: '发现新版本',
      currentVersion: '当前版本',
      newVersion: '最新版本',
      later: 'Mettre à jour plus tard',
      downloadNow: '立即下载',
      unusedCreditsWarning: 'Usage Reminder',
      unusedCreditsMessage:
        'You still have {count} advanced model usage credits left. Are you sure you want to switch accounts?',
      confirmSwitch: 'Confirm',
      cancelSwitch: 'Cancel',
      ccStatus: 'CC Status',
      registerTime: 'Register Time',
      insufficientCredits: 'Insufficient credits, please recharge first',
      email: 'Email',
      cpUsage: 'CP Credits Usage',
      advancedModelUsage: 'Advanced Model Usage',
      basicModelUsage: 'Basic Model Usage',
      cannotGetUsage: 'Cannot Connect',
      cursorHistoryDownload: 'Cursor History Versions Download',
      cursorDbError: 'Cursor database does not exist or data not found',
      cursorNetworkError: 'Network connection error, please check your network',
      cursorDataError: 'Cursor returned data format abnormal',
      cursorUnknownError: 'Cursor unknown error, please restart the software',
      cursorProUnlimitedTip:
        'Cursor latest policy changed to Pro and Pro trial users can call models unlimited',
    },
    login: {
      title: 'Login',
      emailPlaceholder: 'Enter your email',
      passwordPlaceholder: 'Enter your password',
      smsCodePlaceholder: 'Enter verification code',
      sendCode: 'Send Code',
      resendCode: 'Resend in {seconds}s',
      loginButton: 'Login',
      registerButton: 'Register',
      forgotPassword: 'Forgot Password?',
      resetPassword: 'Reset Password',
      emailError: 'Please enter a valid email address',
      passwordError: 'Please enter your password',
      loginSuccess: 'Login successful',
      loginFailed: 'Login failed',
      noAccount: 'No account?',
      register: 'Register Now',
      hasAccount: 'Already have an account? Login',
      userExists: 'This email is registered, switched to login mode',
      userNotExists: 'This email is not registered, please register first',
      emailInvalid: 'Please enter a valid email address',
      emailUnsupported: 'This email domain is not supported',
      passwordInvalid:
        'Password must contain at least 8 characters, including uppercase, lowercase letters and numbers',
    },
    settings: {
      activation: 'Activation',
      activationCode: 'Activation Code',
      activate: 'Activate',
      changePassword: 'Change Password',
      currentPassword: 'Current Password',
      newPassword: 'New Password',
      confirmPassword: 'Confirm Password',
      about: 'About',
      globalPreferences: 'Global Preferences',
      closeMethod: 'Close Method',
      operationMode: 'Operation Mode',
      simpleMode: 'Simple Mode',
      advancedMode: 'Advanced Mode',
      switchedToAdvanced: 'Switched to advanced mode',
      switchedToSimple: 'Switched to simple mode',
      settingsFailed: 'Settings failed',
      route: 'Route',
    },
    history: {
      title: 'Operation History',
      filter: 'Filter',
      dateRange: 'Select Date Range',
      type: 'Type',
      detail: 'Detail',
      time: 'Time',
      operator: 'Operator',
      datePlaceholder: 'Select Date Range',
      clearHistory: 'Clear History',
      clearSuccess: 'History cleared successfully',
      clearFailed: 'Failed to clear history',
    },
    message: {
      pleaseInputActivationCode: 'Please input activation code',
      activationSuccess: 'Activation successful',
      activationFailed: 'Activation failed',
      pleaseInputEmail: 'Please input email',
      addSuccess: 'Added successfully',
      switchSuccess: 'Switched to account: {email}',
      deleteSuccess: 'Deleted successfully',
      pleaseInputPassword: 'Please input all password fields',
      passwordNotMatch: 'New passwords do not match',
      passwordChangeSuccess: 'Password changed successfully',
      passwordChangeFailed: 'Failed to change password',
    },
    systemControl: {
      title: 'System Control',
      hookStatus: 'Hook Status',
      hookApplied: 'Applied',
      hookNotApplied: 'Not Applied',
      applyHook: 'Apply Hook',
      restoreHook: 'Restore Hook',
      updateStatus: 'Auto Update Status',
      updateDisabled: 'Disabled',
      updateEnabled: 'Enabled',
      disableUpdate: 'Disable Update',
      restoreUpdate: 'Restore Update',
      clientStatus: 'Client Status',
      clientVerified: 'Verified',
      clientUnverified: 'Unverified',
      cursorPath: 'Cursor Path: ',
      changePath: 'Change Path',
      systemNotification: 'System Notification',
      authorized: 'Authorized',
      unauthorized: 'Unauthorized',
      requestPermission: 'Request Permission',
      messages: {
        disableUpdateSuccess: 'Auto update disabled successfully',
        restoreUpdateSuccess: 'Auto update restored successfully',
        applyHookSuccess: 'Hook applied successfully',
        restoreHookSuccess: 'Hook restored successfully',
        cursorRunning: 'Cursor is running, please save your work before continuing!',
        forceKillConfirm: 'I have saved, force close',
        permissionGranted: 'Notification permission granted',
        permissionDenied:
          'Permission denied, please enable notifications manually in system settings',
      },
      history: {
        disableUpdate: 'Desactivar actualización automática',
        restoreUpdate: 'Restaurar actualización automática',
        applyHook: 'Aplicar hook',
        restoreHook: 'Restaurar hook',
      },
    },
    about: {
      title: 'About',
      version: 'Version',
      appName: 'Cursor Pool',
      copyright: 'Copyright',
      license:
        'Open-sourced under MIT License. Copyright notice must be preserved when modified or distributed.',
      allRightsReserved: 'All Rights Reserved',
    },
    language: {
      title: 'Language Settings',
      switch: 'Switch Language',
    },
    common: {
      logout: 'Logout',
      copySuccess: 'Copy successful',
      copyFailed: 'Copy failed',
      forceClose: 'I have saved, force close',
      cursorRunning: 'Cursor is running',
      cursorRunningMessage:
        'Cursor läuft, bitte speichern Sie das Projekt, das noch nicht geändert wurde, bevor Sie fortfahren!',
      closingCursor: 'Closing Cursor...',
      forgotPassword: 'Forgot password?',
      unknown: 'Unknown',
      notBound: 'Not Bound',
      verified: 'Verified',
      unverified: 'Unverified',
      success: 'Success',
      failed: 'Failed',
      loading: 'Loading...',
      confirmRestart: 'Restart Now',
      timeUnknown: 'Unknown',
      timeExpired: 'Expired',
      timeDays: 'days',
      timeHours: 'heures',
      timeMinutes: 'minutes',
      statusUnknown: 'Unknown Status',
    },
    inbound: {
      title: 'Route',
      selector: 'Select Route',
      switchSuccess: 'Switched to route: {name}',
      switchFailed: 'Failed to switch route',
      restartNeeded:
        'It is recommended to restart the application to ensure the new route configuration takes effect',
      defaultInbound: 'Default Route',
      domestic: 'Domestic Route',
      foreign: 'Foreign Route',
    },
    closeType: {
      ask: 'Ask Every Time',
      minimize: 'Minimize',
      exit: 'Exit Program',
    },
    historyAccount: {
      title: 'Account History',
      email: 'Email',
      machineCode: 'Machine Code',
      advancedModelUsage: 'Advanced Model Usage',
      actions: 'Actions',
      switchButton: 'Switch',
      deleteButton: 'Delete',
      refreshAll: 'Refresh All',
      clearHighUsage: 'Clear High Usage',
      switchSuccess: 'Account switched successfully',
      switchFailed: 'Failed to switch account',
      deleteSuccess: 'Deleted successfully',
      deleteFailed: 'Failed to delete',
      hookFailed: 'Injection failed, please try manually injecting',
      refreshSuccess: 'All accounts refreshed successfully',
      refreshPartial: 'Successfully refreshed {success}/{total} accounts',
      refreshFailed: 'Failed to refresh usage',
      noHighUsageAccounts: 'No high usage accounts to clear',
      clearHighUsageSuccess: 'Successfully cleared {count} high usage accounts',
      clearHighUsageFailed: 'Failed to clear high usage accounts',
      loadFailed: 'Failed to load account history',
    },
    notification: {
      testTitle: 'Notification Test',
      testBody: 'Congratulations! Notification function has been successfully enabled.',
    },
    network: {
      retrying: 'Retrying {endpoint}, attempt {attempt}/{max}',
      refreshingInbound: 'Refreshing inbound route...',
      inboundRefreshed: 'Inbound route refreshed',
      requestFailed: 'Request failed, retrying connection',
      connectionRestored: 'Connection restored',
    },
  },
  'ja-JP': {
    appName: 'Cursor Pool',
    menu: {
      dashboard: 'ダッシュボード',
      historyAccount: 'アカウント履歴',
      history: '履歴',
      settings: '設定',
    },
    dashboard: {
      deviceInfo: 'デバイス情報',
      machineCode: 'マシンコード',
      currentAccount: '現在のアカウント',
      expiryDate: '有効期限',
      usageStats: '使用統計',
      advancedModel: '高度なモデル使用量',
      normalModel: '通常モデル使用量',
      quickActions: 'クイックアクション',
      changeAccount: 'アカウント変更',
      changeMachineCode: 'マシンコード変更',
      quickChange: 'クイック変更',
      changeSuccess: '変更成功',
      changeFailed: '変更失敗',
      accountChangeSuccess: 'アカウント変更成功',
      accountChangeFailed: 'アカウント変更失敗',
      machineChangeSuccess: 'マシンコード変更成功',
      machineChangeFailed: 'マシンコード変更失敗',
      userInfo: 'ユーザー情報',
      level: '会員等級',
      username: 'ユーザー名',
      expireTime: '有効期限',
      dailyUsage: '毎日使用量',
      cursorInfo: 'Cursor 情報',
      cursorAccount: 'Cursor アカウント',
      cursorUsage: 'Cursor 使用量',
      notLoggedIn: '未ログイン',
      unlimited: '無制限',
      serverNotConnected: '未接続',
      codeUnused: '未使用',
      codeExpired: '已过期',
      codeRefunded: '已退款',
      codeEnded: '已结束',
      memberLevel: {
        1: 'コーダー',
        2: 'プログラマー',
        3: 'エンジニア',
        4: 'アーキテクト',
        5: 'テクニカルディレクター',
      },
      newVersionAvailable: '発見新バージョン',
      currentVersion: '現在のバージョン',
      newVersion: '最新バージョン',
      later: '後で更新',
      downloadNow: '今すぐダウンロード',
      ccStatus: 'CC状態',
      registerTime: '登録時間',
      insufficientCredits: 'クレジット不足、最初に再チャージする必要があります',
      email: 'メールアドレス',
      cpUsage: 'CPクレジット使用量',
      advancedModelUsage: '高度なモデル使用量',
      basicModelUsage: '基本モデル使用量',
      cannotGetUsage: '取得できません',
      cursorHistoryDownload: 'Cursor 履歴バージョン ダウンロード',
      cursorDbError: 'Cursorデータベースが存在しないかデータが見つかりません',
      cursorNetworkError: 'ネットワーク接続エラー、ネットワークを確認してください',
      cursorDataError: 'Cursorから返されたデータ形式が異常です',
      cursorUnknownError: 'Cursor未知のエラー、ソフトウェアを再起動してください',
      cursorProUnlimitedTip:
        'Cursor latest policy changed to Pro and Pro trial users can call models unlimited',
    },
    login: {
      title: 'ログイン',
      emailPlaceholder: 'メールアドレスを入力してください',
      passwordPlaceholder: 'パスワードを入力してください',
      smsCodePlaceholder: '認証コードを入力してください',
      sendCode: '認証コードを送信',
      resendCode: '{seconds}秒後に再送信',
      loginButton: 'ログイン',
      registerButton: '登録',
      forgotPassword: 'パスワードを忘れた場合',
      emailError: '有効なメールアドレスを入力してください',
      passwordError: 'パスワードを入力してください',
      loginSuccess: 'ログイン成功',
      loginFailed: 'ログイン失敗',
      noAccount: 'アカウントをお持ちでない方は',
      register: 'こちらから登録',
      hasAccount: '既にアカウントをお持ちの方は',
      userExists: 'このメールアドレスは既に登録されています。ログインモードに切り替えます',
      userNotExists: 'このメールアドレスは未登録です。先に登録してください',
      emailInvalid: '有効なメールアドレスを入力してください',
      emailUnsupported: 'このメールドメインは現在サポートされていません',
      passwordInvalid:
        'Password must contain at least 8 characters, including uppercase, lowercase letters and numbers',
    },
    settings: {
      activation: 'アクティベーション',
      activationCode: 'アクティベーションコード',
      activate: '有効化',
      changePassword: 'パスワード変更',
      currentPassword: '現在のパスワード',
      newPassword: '新しいパスワード',
      confirmPassword: '新しいパスワード確認',
      about: 'バージョン情報',
      globalPreferences: 'グローバル設定',
      closeMethod: '閉じる方法',
      operationMode: '操作モード',
      simpleMode: 'シンプルモード',
      advancedMode: '高度なモード',
      switchedToAdvanced: '高度なモードに切り替えました',
      switchedToSimple: 'シンプルモードに切り替えました',
      settingsFailed: '設定に失敗しました',
      route: 'ルート',
    },
    history: {
      title: '操作履歴',
      filter: 'フィルター',
      dateRange: '日付範囲を選択',
      type: '操作タイプ',
      detail: '詳細',
      time: '時間',
      operator: '操作者',
      datePlaceholder: '選択日付範囲',
      clearHistory: '履歴をクリア',
      clearSuccess: '履歴が正常にクリアされました',
      clearFailed: '履歴のクリアに失敗しました',
    },
    message: {
      pleaseInputActivationCode: 'アクティベーションコードを入力してください',
      activationSuccess: 'アクティベーションに成功しました',
      activationFailed: 'アクティベーションに失敗しました',
      pleaseInputEmail: 'メールアドレスを入力してください',
      addSuccess: '追加に成功しました',
      switchSuccess: 'アカウントを切り替えました: {email}',
      deleteSuccess: '削除に成功しました',
      pleaseInputPassword: 'すべてのパスワード欄を入力してください',
      passwordNotMatch: '新しいパスワードが一致しません',
      passwordChangeSuccess: 'パスワードの変更に成功しました',
      passwordChangeFailed: 'パスワードの変更に失敗しました',
    },
    systemControl: {
      title: 'システム制御',
      hookStatus: 'フック状態',
      hookApplied: '適用済み',
      hookNotApplied: '未適用',
      applyHook: 'フックを適用',
      restoreHook: 'フックを復元',
      updateStatus: '自動更新状態',
      updateDisabled: '無効',
      updateEnabled: '有効',
      disableUpdate: '更新を無効にする',
      restoreUpdate: 'Restore Update',
      clientStatus: 'クライアント状態',
      clientVerified: '検証済み',
      clientUnverified: '未検証',
      cursorPath: 'Cursorパス: ',
      changePath: '変更パス',
      systemNotification: 'システム通知',
      authorized: '承認済み',
      unauthorized: '未承認',
      requestPermission: '権限をリクエスト',
      messages: {
        disableUpdateSuccess: '自動更新を無効化しました',
        restoreUpdateSuccess: '自動更新を復元しました',
        applyHookSuccess: 'フックを適用しました',
        restoreHookSuccess: 'フックを復元しました',
        cursorRunning: 'Cursorが実行中です。続行する前に作業を保存してください！',
        forceKillConfirm: '保存しました、強制終了します',
        permissionGranted: '通知の権限が付与されました',
        permissionDenied: '権限が拒否されました。システム設定で手動で通知を有効にしてください',
      },
      history: {
        disableUpdate: '自動更新を無効化',
        restoreUpdate: '自動更新を復元',
        applyHook: 'フックを適用',
        restoreHook: 'フックを復元',
      },
    },
    about: {
      title: 'バージョン情報',
      version: 'バージョン',
      appName: 'Cursor Pool',
      copyright: '著作権情報',
      license:
        'MITライセンスの下でオープンソース化。変更や配布の際は著作権表示を保持する必要があります。',
      allRightsReserved: '全著作権所有',
    },
    language: {
      title: '言語設定',
      switch: '言語を切り替え',
    },
    common: {
      logout: 'ログアウト',
      copySuccess: 'コピーに成功しました',
      copyFailed: 'コピーに失敗しました',
      forceClose: '保存しました、強制終了します',
      cursorRunning: 'Cursorが実行中です',
      cursorRunningMessage:
        'Cursorが実行中です。続行する前に未保存のプロジェクトを保存してください！',
      closingCursor: 'Cursor を閉じています...',
      forgotPassword: 'パスワードを忘れた場合',
      confirmRestart: '今すぐ再起動',
      timeUnknown: '不明',
      timeExpired: '期限切れ',
      timeDays: '日',
      timeHours: '時間',
      timeMinutes: '分',
      statusUnknown: '不明',
    },
    inbound: {
      title: 'ルート',
      selector: 'ルートを選択',
      switchSuccess: 'ルートを切り替えました: {name}',
      switchFailed: 'ルートの切り替えに失敗しました',
      restartNeeded: '新しいルート設定を有効にするためにアプリケーションの再起動をお勧めします',
      defaultInbound: 'デフォルトルート',
      domestic: '国内ルート',
      foreign: '海外ルート',
    },
    closeType: {
      ask: '毎回確認',
      minimize: '最小化',
      exit: 'プログラムを終了',
    },
    historyAccount: {
      title: 'アカウント履歴',
      email: 'メールアドレス',
      machineCode: 'マシンコード',
      advancedModelUsage: '高度なモデル使用量',
      actions: '操作',
      switchButton: '切り替え',
      deleteButton: '削除',
      refreshAll: 'すべて更新',
      clearHighUsage: '高使用量アカウントをクリア',
      switchSuccess: 'アカウント切り替え成功',
      switchFailed: 'アカウント切り替え失敗',
      deleteSuccess: '削除成功',
      deleteFailed: '削除失敗',
      hookFailed: '注入失敗、手動での注入を試してください',
      refreshSuccess: 'モ든 アカウントが更新されました',
      refreshPartial: '{success}/{total} アカウントの更新に成功しました',
      refreshFailed: '使用状況の更新に失敗しました',
      noHighUsageAccounts: 'クリアする高使用量アカウントはありません',
      clearHighUsageSuccess: '{count}個の高使用量アカウントをクリアしました',
      clearHighUsageFailed: '高使用量アカウントのクリアに失敗しました',
      loadFailed: 'アカウント履歴の読み込みに失敗しました',
    },
    notification: {
      testTitle: '通知テスト',
      testBody: 'おめでとうございます！通知機能が正常に有効になりました。',
    },
    network: {
      retrying: '再試行中 {endpoint}，第 {attempt}/{max} 回',
      refreshingInbound: 'ルートを更新中...',
      inboundRefreshed: 'ルート更新完了',
      requestFailed: '要求失敗、再接続を試みています',
      connectionRestored: '接続が回復しました',
    },
  },
  'fr-FR': {
    appName: 'Cursor Pool',
    menu: {
      dashboard: 'Tableau de bord',
      historyAccount: 'Historique des comptes',
      history: 'Historique',
      settings: 'Paramètres',
    },
    dashboard: {
      deviceInfo: 'Informations sur le périphérique',
      machineCode: 'Code de la machine',
      currentAccount: 'Compte actuel',
      expiryDate: "Date d'expiration",
      usageStats: "Statistiques d'utilisation",
      advancedModel: 'Utilisation du modèle avancé',
      normalModel: 'Utilisation du modèle normal',
      quickActions: 'Actions rapides',
      changeAccount: 'Changer de compte',
      changeMachineCode: 'Changer le code de la machine',
      quickChange: 'Changement rapide (compte + code de la machine)',
      changeSuccess: 'Changement réussi',
      changeFailed: 'Changement échoué',
      accountChangeSuccess: 'Changement de compte réussi',
      accountChangeFailed: 'Changement de compte échoué',
      machineChangeSuccess: 'Changement de machine réussi',
      machineChangeFailed: 'Changement de machine échoué',
      userInfo: 'Informations utilisateur',
      level: 'Niveau de membre',
      username: "Nom d'utilisateur",
      expireTime: "Date d'expiration",
      dailyUsage: 'Utilisation quotidienne',
      cursorInfo: 'Informations sur le curseur',
      cursorAccount: 'Compte du curseur',
      cursorUsage: 'Utilisation du curseur',
      notLoggedIn: 'Non connecté',
      unlimited: 'Illimité',
      serverNotConnected: 'Non connecté',
      codeUnused: 'Non utilisé',
      codeExpired: 'Expiré',
      codeRefunded: 'Remboursé',
      codeEnded: 'Terminé',
      memberLevel: {
        1: 'Codeur',
        2: 'Programmeur',
        3: 'Ingénieur',
        4: 'Architecte',
        5: 'Directeur technique',
      },
      newVersionAvailable: 'Découvrez la nouvelle version',
      currentVersion: 'Version actuelle',
      newVersion: 'Nouvelle version',
      later: 'Mettre à jour plus tard',
      downloadNow: 'Télécharger maintenant',
      unusedCreditsWarning: "Rappel d'utilisation",
      unusedCreditsMessage:
        "Il vous reste {count} crédits d'utilisation du modèle avancé. Êtes-vous sûr de vouloir changer de compte ?",
      confirmSwitch: 'Confirmer',
      cancelSwitch: 'Annuler',
      ccStatus: 'État CC',
      registerTime: "Heure d'inscription",
      insufficientCredits: "Crédits insuffisants, veuillez recharger d'abord",
      email: 'Email',
      cpUsage: 'Utilisation des crédits CP',
      advancedModelUsage: 'Utilisation du modèle avancé',
      basicModelUsage: 'Utilisation du modèle basique',
      cannotGetUsage: 'Impossible de se connecter',
      cursorHistoryDownload: 'Télécharger les versions historiques de Cursor',
      cursorDbError: "La base de données Cursor n'existe pas ou les données sont introuvables",
      cursorNetworkError: 'Erreur de connexion réseau, veuillez vérifier votre réseau',
      cursorDataError: 'Format de données anormal retourné par Cursor',
      cursorUnknownError: 'Erreur inconnue de Cursor, veuillez redémarrer le logiciel',
      cursorProUnlimitedTip:
        'Cursor latest policy changed to Pro and Pro trial users can call models unlimited',
    },
    login: {
      title: 'Connexion',
      emailPlaceholder: 'Entrez votre adresse e-mail',
      passwordPlaceholder: 'Entrez votre mot de passe',
      smsCodePlaceholder: 'Entrez le code de vérification',
      sendCode: 'Envoyer le code',
      resendCode: 'Réenvoyer dans {seconds}s',
      loginButton: 'Connexion',
      registerButton: 'Inscription',
      forgotPassword: 'Mot de passe oublié ?',
      emailError: 'Veuillez entrer une adresse e-mail valide',
      passwordError: 'Veuillez entrer votre mot de passe',
      loginSuccess: 'Connexion réussie',
      loginFailed: 'Connexion échouée',
      noAccount: 'Pas de compte ?',
      register: "S'inscrire maintenant",
      hasAccount: 'Vous avez déjà un compte ? Connectez-vous',
      userExists: 'Cet email est déjà enregistré, passage en mode connexion',
      userNotExists: "Cet email n'est pas enregistré, veuillez vous inscrire d'abord",
      emailInvalid: 'Veuillez entrer une adresse e-mail valide',
      emailUnsupported: "Ce domaine e-mail n'est pas supporté",
      passwordInvalid:
        'Password must contain at least 8 characters, including uppercase, lowercase letters and numbers',
    },
    settings: {
      activation: 'Activation',
      activationCode: "Code d'activation",
      activate: 'Activer',
      changePassword: 'Changer le mot de passe',
      currentPassword: 'Mot de passe actuel',
      newPassword: 'Nouveau mot de passe',
      confirmPassword: 'Confirmer le mot de passe',
      about: 'À propos',
      globalPreferences: 'Préférences globales',
      closeMethod: 'Méthode de fermeture',
      operationMode: "Mode d'opération",
      simpleMode: 'Mode simple',
      advancedMode: 'Mode avancé',
      switchedToAdvanced: 'Passé au mode avancé',
      switchedToSimple: 'Passé au mode simple',
      settingsFailed: 'Échec des paramètres',
      route: 'Route',
    },
    history: {
      title: 'Historique des opérations',
      filter: 'Filtrer',
      dateRange: 'Sélectionner la période',
      type: 'Type',
      detail: 'Détail',
      time: 'Heure',
      operator: 'Opérateur',
      datePlaceholder: 'Sélectionner la plage de dates',
      clearHistory: "Vider l'historique",
      clearSuccess: 'Historique vidé avec succès',
      clearFailed: "Échec de la vidage de l'historique",
    },
    message: {
      pleaseInputActivationCode: "Veuillez saisir le code d'activation",
      activationSuccess: 'Activation réussie',
      activationFailed: 'Activation échouée',
      pleaseInputEmail: "Veuillez saisir l'email",
      addSuccess: 'Ajout réussi',
      switchSuccess: 'Changement de compte effectué: {email}',
      deleteSuccess: 'Suppression réussie',
      pleaseInputPassword: 'Veuillez remplir tous les champs de mot de passe',
      passwordNotMatch: 'Les mots de passe ne correspondent pas',
      passwordChangeSuccess: 'Mot de passe modifié avec succès',
      passwordChangeFailed: 'Échec de la modification du mot de passe',
    },
    systemControl: {
      title: 'Contrôle système',
      hookStatus: 'Statut du hook',
      hookApplied: 'Appliqué',
      hookNotApplied: 'Non appliqué',
      applyHook: 'Appliquer le hook',
      restoreHook: 'Restaurer le hook',
      updateStatus: 'État de mise à jour',
      updateDisabled: 'Désactivé',
      updateEnabled: 'Activé',
      disableUpdate: 'Désactiver la mise à jour',
      restoreUpdate: 'Restaurar obnovení',
      clientStatus: 'État du client',
      clientVerified: 'Vérifié',
      clientUnverified: 'Non vérifié',
      cursorPath: 'Chemin Cursor: ',
      changePath: 'Changer le chemin',
      systemNotification: 'Notification système',
      authorized: 'Autorisé',
      unauthorized: 'Non autorisé',
      requestPermission: 'Demander une autorisation',
      messages: {
        disableUpdateSuccess: 'Auto update disabled successfully',
        restoreUpdateSuccess: 'Auto update restored successfully',
        applyHookSuccess: 'Hook aplicado con éxito',
        restoreHookSuccess: 'Hook restaurado con éxito',
        cursorRunning: 'Cursor en ejecución, por favor guarde su trabajo antes de continuar!',
        forceKillConfirm: 'He guardado, forzar cierre',
        permissionGranted: 'Autorisation de notification accordée',
        permissionDenied:
          'Permission refusée, veuillez activer les notifications manuellement dans les paramètres système',
      },
      history: {
        disableUpdate: 'Desactivar actualización automática',
        restoreUpdate: 'Restaurar actualización automática',
        applyHook: 'Aplicar hook',
        restoreHook: 'Restaurar hook',
      },
    },
    about: {
      title: 'À propos',
      version: 'Version',
      appName: 'Cursor Pool',
      copyright: 'Copyright',
      license:
        'Open-sourced under MIT License. Copyright notice must be preserved when modified or distributed.',
      allRightsReserved: 'All Rights Reserved',
    },
    language: {
      title: 'Paramètres de langue',
      switch: 'Basculer la langue',
    },
    common: {
      logout: 'Se déconnecter',
      copySuccess: 'Copie réussie',
      copyFailed: 'Échec de la copie',
      forceClose: "J'ai sauvegardé, fermer forcément",
      cursorRunning: "Cursor en cours d'exécution",
      cursorRunningMessage:
        "Détecté que le Cursor est en cours d'exécution, veuillez enregistrer le projet qui n'a pas été modifié avant de continuer!",
      closingCursor: 'Fermeture du Cursor...',
      forgotPassword: 'Mot de passe oublié?',
      confirmRestart: 'Redémarrer maintenant',
      timeUnknown: 'Inconnu',
      timeExpired: 'Expiré',
      timeDays: 'jours',
      timeHours: 'heures',
      timeMinutes: 'minutes',
      statusUnknown: 'État inconnu',
    },
    inbound: {
      title: 'Route',
      selector: 'Sélectionner la route',
      switchSuccess: 'Changement de route effectué : {name}',
      switchFailed: 'Échec du changement de route',
      restartNeeded:
        "Il est recommandé de redémarrer l'application pour que la nouvelle configuration de route prenne effet",
      defaultInbound: 'Route par défaut',
      domestic: 'Route nationale',
      foreign: 'Route internationale',
    },
    closeType: {
      ask: 'Demander à chaque fois',
      minimize: 'Minimiser',
      exit: 'Quitter le programme',
    },
    historyAccount: {
      title: 'Historique des comptes',
      email: 'Email',
      machineCode: 'Code machine',
      advancedModelUsage: 'Utilisation du modèle avancé',
      actions: 'Actions',
      switchButton: 'Changer',
      deleteButton: 'Supprimer',
      refreshAll: 'Actualiser tout',
      clearHighUsage: 'Limpiar cuentas de alto uso',
      switchSuccess: 'Compte changé avec succès',
      switchFailed: 'Échec du changement de compte',
      deleteSuccess: 'Suppression réussie',
      deleteFailed: 'Échec de la suppression',
      hookFailed: "Échec de l'injection, veuillez essayer manuellement",
      refreshSuccess: 'Tous les comptes ont été actualisés avec succès',
      refreshPartial: 'Actualisé avec succès {success}/{total} comptes',
      refreshFailed: "Échec de l'actualisation de l'utilisation",
      noHighUsageAccounts: 'Aucun compte à haute utilisation à effacer',
      clearHighUsageSuccess: 'Effacé avec succès {count} comptes à haute utilisation',
      clearHighUsageFailed: "Échec de l'effacement des comptes à haute utilisation",
      loadFailed: "Échec du chargement de l'historique des comptes",
    },
    notification: {
      testTitle: 'Test de Notification',
      testBody: 'Félicitations ! La fonction de notification a été activée avec succès.',
    },
    network: {
      retrying: 'Réessayant {endpoint}, essai {attempt}/{max}',
      refreshingInbound: 'Mise à jour de la route...',
      inboundRefreshed: 'Route mise à jour terminée',
      requestFailed: 'Échec de la requête, réessayage de la connexion',
      connectionRestored: 'Connexion restaurée',
    },
  },
  'de-DE': {
    appName: 'Cursor Pool',
    menu: {
      dashboard: 'Übersicht',
      historyAccount: 'Kontoverlauf',
      history: 'Verlauf',
      settings: 'Einstellungen',
    },
    dashboard: {
      deviceInfo: 'Geräteinformationen',
      machineCode: 'Maschinencode',
      currentAccount: 'Aktuelles Konto',
      expiryDate: 'Ablaufdatum',
      usageStats: 'Nutzungsstatistiken',
      advancedModel: 'Erweiterte Modellnutzung',
      normalModel: 'Normale Modellnutzung',
      quickActions: 'Schnellaktionen',
      changeAccount: 'Konto wechseln',
      changeMachineCode: 'Maschinencode ändern',
      quickChange: 'Schnellwechsel (Konto + Maschinencode)',
      changeSuccess: 'Änderung erfolgreich',
      changeFailed: 'Änderung fehlgeschlagen',
      accountChangeSuccess: 'Kontoänderung erfolgreich',
      accountChangeFailed: 'Kontoänderung fehlgeschlagen',
      machineChangeSuccess: 'Maschinencodeänderung erfolgreich',
      machineChangeFailed: 'Maschinencodeänderung fehlgeschlagen',
      userInfo: 'Benutzerinformationen',
      level: 'Mitgliedschaftslevel',
      username: 'Benutzername',
      expireTime: 'Ablaufdatum',
      dailyUsage: 'Tägliche Nutzung',
      cursorInfo: 'Cursor-Informationen',
      cursorAccount: 'Cursor-Konto',
      cursorUsage: 'Cursor-Nutzung',
      notLoggedIn: 'Nicht angemeldet',
      unlimited: 'Unbegrenzt',
      serverNotConnected: 'Nicht verbunden',
      codeUnused: 'Nicht verwendet',
      codeExpired: 'Abgelaufen',
      codeRefunded: 'Erstattet',
      codeEnded: 'Beendet',
      memberLevel: {
        1: 'Coder',
        2: 'Programmierer',
        3: 'Ingenieur',
        4: 'Architekt',
        5: 'Technischer Direktor',
      },
      newVersionAvailable: 'Neue Version entdeckt',
      currentVersion: 'Aktuelle Version',
      newVersion: 'Neue Version',
      later: 'Später aktualisieren',
      downloadNow: 'Jetzt herunterladen',
      ccStatus: 'CC Status',
      registerTime: 'Registrierungszeit',
      insufficientCredits: 'Unzureichende Guthaben, bitte zuerst aufladen',
      email: 'Email',
      cpUsage: 'CP-Kreditnutzung',
      advancedModelUsage: 'Erweiterte Modellnutzung',
      basicModelUsage: 'Grundlegende Modellnutzung',
      cannotGetUsage: 'Keine Verbindung möglich',
      cursorHistoryDownload: 'Cursor Historische Versionen Herunterladen',
      cursorDbError: 'Cursor-Datenbank existiert nicht oder Daten nicht gefunden',
      cursorNetworkError: 'Netzwerkverbindungsfehler, bitte überprüfen Sie Ihr Netzwerk',
      cursorDataError: 'Cursor lieferte abnormales Datenformat zurück',
      cursorUnknownError: 'Cursor unbekannter Fehler, bitte starten Sie die Software neu',
      cursorProUnlimitedTip:
        'Cursor latest policy changed to Pro and Pro trial users can call models unlimited',
    },
    login: {
      title: 'Anmeldung',
      emailPlaceholder: 'E-Mail-Adresse eingeben',
      passwordPlaceholder: 'Passwort eingeben',
      smsCodePlaceholder: 'Verifizierungscode eingeben',
      sendCode: 'Verifizierungscode senden',
      resendCode: 'In {seconds}s erneut senden',
      loginButton: 'Anmelden',
      registerButton: 'Registrieren',
      forgotPassword: 'Passwort vergessen?',
      emailError: 'Bitte geben Sie eine gültige E-Mail-Adresse ein',
      passwordError: 'Bitte geben Sie Ihr Passwort ein',
      loginSuccess: 'Anmeldung erfolgreich',
      loginFailed: 'Anmeldung fehlgeschlagen',
      noAccount: 'Kein Konto?',
      register: 'Jetzt registrieren',
      hasAccount: 'Sie haben bereits ein Konto? Anmelden',
      userExists: 'Diese E-Mail ist bereits registriert, Wechsel zum Anmeldemodus',
      userNotExists: 'Diese E-Mail ist nicht registriert, bitte registrieren Sie sich zuerst',
      emailInvalid: 'Bitte geben Sie eine gültige E-Mail-Adresse ein',
      emailUnsupported: 'Dieser E-Mail-Domain ist derzeit keine Unterstützung verfügbar',
      passwordInvalid:
        'Password must contain at least 8 characters, including uppercase, lowercase letters and numbers',
    },
    settings: {
      activation: 'Aktivierung',
      activationCode: 'Aktivierungscode',
      activate: 'Aktivieren',
      changePassword: 'Passwort ändern',
      currentPassword: 'Aktuelles Passwort',
      newPassword: 'Neues Passwort',
      confirmPassword: 'Passwort bestätigen',
      about: 'Über',
      globalPreferences: 'Globale Einstellungen',
      closeMethod: 'Schließmethode',
      operationMode: 'Betriebsmodus',
      simpleMode: 'Einfacher Modus',
      advancedMode: 'Erweiterter Modus',
      switchedToAdvanced: 'Zum erweiterten Modus gewechselt',
      switchedToSimple: 'Zum einfachen Modus gewechselt',
      settingsFailed: 'Einstellungen fehlgeschlagen',
      route: 'Route',
    },
    history: {
      title: 'Operationsverlauf',
      filter: 'Filter',
      dateRange: 'Datumsbereich auswählen',
      type: 'Typ',
      detail: 'Detail',
      time: 'Zeit',
      operator: 'Benutzer',
      datePlaceholder: 'Datumsbereich auswählen',
      clearHistory: 'Verlauf löschen',
      clearSuccess: 'Verlauf erfolgreich gelöscht',
      clearFailed: 'Verlauf löschen fehlgeschlagen',
    },
    message: {
      pleaseInputActivationCode: 'Bitte Aktivierungscode eingeben',
      activationSuccess: 'Aktivierung erfolgreich',
      activationFailed: 'Aktivierung fehlgeschlagen',
      pleaseInputEmail: 'Bitte E-Mail eingeben',
      addSuccess: 'Erfolgreich hinzugefügt',
      switchSuccess: 'Zu Konto gewechselt: {email}',
      deleteSuccess: 'Erfolgreich gelöscht',
      pleaseInputPassword: 'Bitte alle Passwortfelder ausfüllen',
      passwordNotMatch: 'Die Passwörter stimmen nicht überein',
      passwordChangeSuccess: 'Passwort erfolgreich geändert',
      passwordChangeFailed: 'Passwortänderung fehlgeschlagen',
    },
    systemControl: {
      title: 'Systemsteuerung',
      hookStatus: 'Hook-Status',
      hookApplied: 'Angewendet',
      hookNotApplied: 'Nicht angewendet',
      applyHook: 'Hook anwenden',
      restoreHook: 'Hook wiederherstellen',
      updateStatus: 'Automatischer Update-Status',
      updateDisabled: 'Deaktiviert',
      updateEnabled: 'Aktiviert',
      disableUpdate: 'Disable Update',
      restoreUpdate: 'Restore Update',
      clientStatus: 'Client-Status',
      clientVerified: 'Verifiziert',
      clientUnverified: 'Nicht verifiziert',
      cursorPath: 'Cursor-Pfad: ',
      changePath: 'Ändern Sie den Pfad',
      systemNotification: 'Systembenachrichtigung',
      authorized: 'Autorisiert',
      unauthorized: 'Nicht autorisiert',
      requestPermission: 'Berechtigung anfordern',
      messages: {
        disableUpdateSuccess: 'Automatische Updates erfolgreich deaktiviert',
        restoreUpdateSuccess: 'Automatische Updates erfolgreich wiederhergestellt',
        applyHookSuccess: 'Hook aplicado con éxito',
        restoreHookSuccess: 'Hook restaurado con éxito',
        cursorRunning: 'Cursor en ejecución, por favor guarde su trabajo antes de continuar!',
        forceKillConfirm: 'He guardado, forzar cierre',
        permissionGranted: 'Benachrichtigungsberechtigung erteilt',
        permissionDenied:
          'Berechtigung verweigert, bitte aktivieren Sie Benachrichtigungen manuell in den Systemeinstellungen',
      },
      history: {
        disableUpdate: 'Desactivar actualización automática',
        restoreUpdate: 'Restaurar actualización automática',
        applyHook: 'Aplicar hook',
        restoreHook: 'Restaurar hook',
      },
    },
    about: {
      title: 'Über',
      version: 'Version',
      appName: 'Cursor Pool',
      copyright: 'Copyright',
      license:
        'Open-sourced under MIT License. Copyright notice must be preserved when modified or distributed.',
      allRightsReserved: 'All Rights Reserved',
    },
    language: {
      title: 'Spracheinstellungen',
      switch: 'Sprache wechseln',
    },
    common: {
      logout: 'Abmelden',
      copySuccess: 'Kopie erfolgreich',
      copyFailed: 'Kopie fehlgeschlagen',
      forceClose: 'Ich habe gespeichert, erzwingend schließen',
      cursorRunning: 'Cursor läuft',
      cursorRunningMessage:
        'Cursor läuft, bitte speichern Sie das Projekt, das noch nicht geändert wurde, bevor Sie fortfahren!',
      closingCursor: 'Cursor schließen...',
      forgotPassword: 'Mot de passe oublié?',
      confirmRestart: 'Jetzt neu starten',
      timeUnknown: 'Unbekannt',
      timeExpired: 'Abgelaufen',
      timeDays: 'Tage',
      timeHours: 'Stunden',
      timeMinutes: 'Minuten',
      statusUnknown: 'Unbekannt',
    },
    inbound: {
      title: 'Route',
      selector: 'Route auswählen',
      switchSuccess: 'Zu Route gewechselt: {name}',
      switchFailed: 'Routenwechsel fehlgeschlagen',
      restartNeeded:
        'Um sicherzustellen, dass die neue Routenkonfiguration wirksam wird, wird ein Neustart der Anwendung empfohlen',
      defaultInbound: 'Standardroute',
      domestic: 'Inlandsroute',
      foreign: 'Auslandsroute',
    },
    closeType: {
      ask: 'Jedes Mal fragen',
      minimize: 'Minimieren',
      exit: 'Programm beenden',
    },
    historyAccount: {
      title: 'Kontoverlauf',
      email: 'E-Mail',
      machineCode: 'Maschinencode',
      advancedModelUsage: 'Erweiterte Modellnutzung',
      actions: 'Aktionen',
      switchButton: 'Wechseln',
      deleteButton: 'Löschen',
      refreshAll: 'Alle aktualisieren',
      clearHighUsage: 'Konten mit hoher Nutzung löschen',
      switchSuccess: 'Konto erfolgreich gewechselt',
      switchFailed: 'Kontowechsel fehlgeschlagen',
      deleteSuccess: 'Erfolgreich gelöscht',
      deleteFailed: 'Löschen fehlgeschlagen',
      hookFailed: 'Injektion fehlgeschlagen, bitte versuchen Sie es manuell',
      refreshSuccess: 'Alle Konten erfolgreich aktualisiert',
      refreshPartial: '{success}/{total} Konten erfolgreich aktualisiert',
      refreshFailed: 'Aktualisierung der Nutzung fehlgeschlagen',
      noHighUsageAccounts: 'Keine Konten mit hoher Nutzung zum Löschen',
      clearHighUsageSuccess: '{count} Konten mit hoher Nutzung erfolgreich gelöscht',
      clearHighUsageFailed: 'Löschen von Konten mit hoher Nutzung fehlgeschlagen',
      loadFailed: 'Laden des Kontoverlaufs fehlgeschlagen',
    },
    notification: {
      testTitle: 'Benachrichtigungstest',
      testBody: 'Glückwunsch! Die Benachrichtigungsfunktion wurde erfolgreich aktiviert.',
    },
    network: {
      retrying: 'Wiederholt {endpoint}, Versuch {attempt}/{max}',
      refreshingInbound: 'Route wird aktualisiert...',
      inboundRefreshed: 'Route aktualisiert',
      requestFailed: 'Anfrage fehlgeschlagen, erneuter Versuch',
      connectionRestored: 'Verbindung wiederhergestellt',
    },
  },
  'ko-KR': {
    appName: 'Cursor Pool',
    menu: {
      dashboard: '대시보드',
      historyAccount: '계정 기록',
      history: '기록',
      settings: '설정',
    },
    dashboard: {
      deviceInfo: '장치 정보',
      machineCode: '기기 코드',
      currentAccount: '현재 계정',
      expiryDate: '만료일',
      usageStats: '사용 통계',
      advancedModel: '고급 모델 사용량',
      normalModel: '일반 모델 사용량',
      quickActions: '빠른 작업',
      changeAccount: '계정 변경',
      changeMachineCode: '기기 코드 변경',
      quickChange: '빠른 변경(계정 + 기기 코드)',
      changeSuccess: '변경 성공',
      changeFailed: '변경 실패',
      accountChangeSuccess: '계정 변경 성공',
      accountChangeFailed: '계정 변경 실패',
      machineChangeSuccess: '기기 코드 변경 성공',
      machineChangeFailed: '기기 코드 변경 실패',
      userInfo: '사용자 정보',
      level: '회원 등급',
      username: '사용자 이름',
      expireTime: '만료 시간',
      dailyUsage: '일일 사용량',
      cursorInfo: 'Cursor 정보',
      cursorAccount: 'Cursor 계정',
      cursorUsage: 'Cursor 사용량',
      notLoggedIn: '로그인하지 않음',
      unlimited: '무제한',
      serverNotConnected: '서버 연결되지 않음',
      codeUnused: '미사용',
      codeExpired: '만료됨',
      codeRefunded: '환불됨',
      codeEnded: '종료됨',
      memberLevel: {
        1: '코더',
        2: '프로그래머',
        3: '엔지니어',
        4: '아키텍트',
        5: '기술 이사',
      },
      newVersionAvailable: '새로운 버전 발견',
      currentVersion: '현재 버전',
      newVersion: '새로운 버전',
      later: '나중에 업데이트',
      downloadNow: '지금 다운로드',
      ccStatus: 'CC 상태',
      registerTime: '등록 시간',
      insufficientCredits: '크레딧 부족, 먼저 재충전하세요',
      email: '이메일',
      cpUsage: 'CP 크레딧 사용량',
      advancedModelUsage: '고급 모델 사용량',
      basicModelUsage: '기본 모델 사용량',
      cannotGetUsage: '연결할 수 없음',
      cursorHistoryDownload: 'Cursor 이전 버전 다운로드',
      cursorDbError: 'Cursor 데이터베이스가 존재하지 않거나 데이터를 찾을 수 없습니다',
      cursorNetworkError: '네트워크 연결 오류, 네트워크를 확인하세요',
      cursorDataError: 'Cursor가 반환한 데이터 형식이 비정상입니다',
      cursorUnknownError: 'Cursor 알 수 없는 오류, 소프트웨어를 재시작하세요',
      cursorProUnlimitedTip:
        'Cursor latest policy changed to Pro and Pro trial users can call models unlimited',
    },
    login: {
      title: '로그인',
      emailPlaceholder: '이메일 주소를 입력하세요',
      passwordPlaceholder: '비밀번호를 입력하세요',
      smsCodePlaceholder: '인증 코드를 입력하세요',
      sendCode: '인증 코드 전송',
      resendCode: '{seconds}초 후 재전송',
      loginButton: '로그인',
      registerButton: '등록',
      forgotPassword: '비밀번호를 잊으셨나요?',
      emailError: '유효한 이메일 주소를 입력하세요',
      passwordError: '비밀번호를 입력하세요',
      loginSuccess: '로그인 성공',
      loginFailed: '로그인 실패',
      noAccount: '계정이 없으신가요?',
      register: '지금 가입하기',
      hasAccount: '이미 계정이 있으신가요? 로그인',
      userExists: '이메일이 이미 등록되어 있습니다. 로그인 모드로 전환합니다',
      userNotExists: '등록되지 않은 이메일입니다. 먼저 회원가입을 해주세요',
      emailInvalid: '유효한 이메일 주소를 입력하세요',
      emailUnsupported: '이 이메인은 현재 지원되지 않습니다',
      passwordInvalid:
        'Password must contain at least 8 characters, including uppercase, lowercase letters and numbers',
    },
    settings: {
      activation: '활성화',
      activationCode: '활성화 코드',
      activate: '활성화하기',
      changePassword: '비밀번호 변경',
      currentPassword: '현재 비밀번호',
      newPassword: '새 비밀번호',
      confirmPassword: '비밀번호 확인',
      about: '정보',
      globalPreferences: '전역 환경설정',
      closeMethod: '종료 방식',
      operationMode: '작동 모드',
      simpleMode: '간편 모드',
      advancedMode: '고급 모드',
      switchedToAdvanced: '고급 모드로 전환되었습니다',
      switchedToSimple: '간편 모드로 전환되었습니다',
      settingsFailed: '설정 실패',
      route: '라우트',
    },
    history: {
      title: '작업 기록',
      filter: '필터',
      dateRange: '날짜 범위 선택',
      type: '유형',
      detail: '상세',
      time: '시간',
      operator: '작업자',
      datePlaceholder: '날짜 범위 선택',
      clearHistory: '기록 지우기',
      clearSuccess: '기록이 성공적으로 지워졌습니다',
      clearFailed: '기록 지우기 실패',
    },
    message: {
      pleaseInputActivationCode: '활성화 코드를 입력해주세요',
      activationSuccess: '활성화 성공',
      activationFailed: '활성화 실패',
      pleaseInputEmail: '이메일을 입력해주세요',
      addSuccess: '추가 성공',
      switchSuccess: '계정 전환 완료: {email}',
      deleteSuccess: '삭제 성공',
      pleaseInputPassword: '모든 비밀번호 필드를 입력해주세요',
      passwordNotMatch: '비밀번호가 일치하지 않습니다',
      passwordChangeSuccess: '비밀번호 변경 성공',
      passwordChangeFailed: '비밀번호 변경 실패',
    },
    systemControl: {
      title: '시스템 제어',
      hookStatus: 'Hook 상태',
      hookApplied: '적용됨',
      hookNotApplied: '미적용',
      applyHook: '훅 적용',
      restoreHook: '훅 복원',
      updateStatus: '자동 업데이트 상태',
      updateDisabled: '비활성화',
      updateEnabled: '활성화',
      disableUpdate: '업데이트 비활성화',
      restoreUpdate: '업데이트 복원',
      clientStatus: '클라이언트 상태',
      clientVerified: '검증됨',
      clientUnverified: '미검증',
      cursorPath: 'Cursor 경로: ',
      changePath: '변경 경로',
      systemNotification: '시스템 알림',
      authorized: '권한 있음',
      unauthorized: '권한 없음',
      requestPermission: '권한 요청',
      messages: {
        disableUpdateSuccess: '자동 업데이트가 비활성화되었습니다',
        restoreUpdateSuccess: '자동 업데이트가 복원되었습니다',
        applyHookSuccess: '훅이 적용되었습니다',
        restoreHookSuccess: '훅이 복원되었습니다',
        cursorRunning: 'Cursor가 실행 중입니다. 계속하기 전에 작업을 저장하세요!',
        forceKillConfirm: '저장했습니다, 강제 종료',
        permissionGranted: '알림 권한이 부여되었습니다',
        permissionDenied: '권한이 거부되었습니다. 시스템 설정에서 수동으로 알림을 활성화하세요',
      },
      history: {
        disableUpdate: '자동 업데이트 비활성화',
        restoreUpdate: '자동 업데이트 복원',
        applyHook: '훅 적용',
        restoreHook: '훅 복원',
      },
    },
    about: {
      title: '정보',
      version: '버전',
      appName: 'Cursor Pool',
      copyright: 'Copyright',
      license:
        'Open-sourced under MIT License. Copyright notice must be preserved when modified or distributed.',
      allRightsReserved: 'All Rights Reserved',
    },
    language: {
      title: '언어 설정',
      switch: '언어 변경',
    },
    common: {
      logout: '로그아웃',
      copySuccess: '복사 성공',
      copyFailed: '복사 실패',
      forceClose: '저장하고 강제로 닫기',
      cursorRunning: 'Cursor 실행 중',
      cursorRunningMessage: '변경되지 않은 프로젝트를 저장한 후 계속하세요!',
      closingCursor: 'Cursor 닫는 중...',
      forgotPassword: '비밀번호를 잊으셨나요?',
      confirmRestart: '지금 재시작',
      timeUnknown: '불명',
      timeExpired: '만료됨',
      timeDays: '일',
      timeHours: '시간',
      timeMinutes: '분',
      statusUnknown: '알 수 없음',
    },
    inbound: {
      title: '라우트',
      selector: '라우트 선택',
      switchSuccess: '라우트로 전환됨: {name}',
      switchFailed: '라우트 전환 실패',
      restartNeeded: '새 라우트 구성이 적용되도록 애플리케이션을 재시작하는 것이 좋습니다',
      defaultInbound: '기본 라우트',
      domestic: '국내 라우트',
      foreign: '해외 라우트',
    },
    closeType: {
      ask: '매번 확인',
      minimize: '최소화',
      exit: '프로그램 종료',
    },
    historyAccount: {
      title: '계정 기록',
      email: '이메일',
      machineCode: '기기 코드',
      advancedModelUsage: '고급 모델 사용량',
      actions: '작업',
      switchButton: '전환',
      deleteButton: '삭제',
      refreshAll: '전체 새로고침',
      clearHighUsage: '높은 사용량 계정 정리',
      switchSuccess: '계정 전환 성공',
      switchFailed: '계정 전환 실패',
      deleteSuccess: '삭제 성공',
      deleteFailed: '삭제 실패',
      hookFailed: '주입 실패, 수동으로 주입해 보세요',
      refreshSuccess: '모든 계정이 성공적으로 새로고침되었습니다',
      refreshPartial: '{success}/{total} 계정 새로고침 성공',
      refreshFailed: '사용량 새로고침 실패',
      noHighUsageAccounts: '정리할 높은 사용량 계정이 없습니다',
      clearHighUsageSuccess: '{count}개의 높은 사용량 계정 정리 성공',
      clearHighUsageFailed: '높은 사용량 계정 정리 실패',
      loadFailed: '계정 기록 로드 실패',
    },
    notification: {
      testTitle: '알림 테스트',
      testBody: '축하합니다! 알림 기능이 성공적으로 활성화되었습니다.',
    },
    network: {
      retrying: '다시 시도중 {endpoint}，{attempt}/{max} 번째',
      refreshingInbound: '라우트 새로고침 중...',
      inboundRefreshed: '라우트 새로고침 완료',
      requestFailed: '요청 실패, 재연결 시도 중',
      connectionRestored: '연결 복구됨',
    },
  },
  'ru-RU': {
    appName: 'Cursor Pool',
    menu: {
      dashboard: 'Обзор',
      historyAccount: 'История аккаунтов',
      history: 'История',
      settings: 'Настройки',
    },
    dashboard: {
      deviceInfo: 'Информация об устройстве',
      machineCode: 'Код устройства',
      currentAccount: 'Текущий аккаунт',
      expiryDate: 'Дата истечения',
      usageStats: 'Статистика использования',
      advancedModel: 'Использование продвинутой модели',
      normalModel: 'Использование обычной модели',
      quickActions: 'Быстрые действия',
      changeAccount: 'Сменить аккаунт',
      changeMachineCode: 'Сменить код устройства',
      quickChange: 'Быстрая смена (аккаунт + код устройства)',
      changeSuccess: 'Изменение успешно',
      changeFailed: 'Изменение не удалось',
      accountChangeSuccess: 'Смена аккаунта успешна',
      accountChangeFailed: 'Смена аккаунта не удалась',
      machineChangeSuccess: 'Смена кода устройства успешна',
      machineChangeFailed: 'Смена кода устройства не удалась',
      userInfo: 'Информация о пользователе',
      level: 'Уровень участия',
      username: 'Имя пользователя',
      expireTime: 'Дата истечения',
      dailyUsage: 'Ежедневное использование',
      cursorInfo: 'Информация о курсоре',
      cursorAccount: 'Аккаунт курсора',
      cursorUsage: 'Использование курсора',
      notLoggedIn: 'Не авторизован',
      unlimited: 'Неограниченный',
      serverNotConnected: 'Не подключено',
      codeUnused: 'Не используется',
      codeExpired: 'Истек срок',
      codeRefunded: 'Возвращен',
      codeEnded: 'Завершен',
      memberLevel: {
        1: 'Кодер',
        2: 'Программист',
        3: 'Инженер',
        4: 'Архитектор',
        5: 'Технический директор',
      },
      newVersionAvailable: 'Обнаружена новая версия',
      currentVersion: 'Текущая версия',
      newVersion: 'Новая версия',
      later: 'Обновить позже',
      downloadNow: 'Скачать сейчас',
      ccStatus: 'Статус CC',
      registerTime: 'Время регистрации',
      insufficientCredits: 'Недостаточно кредитов, пожалуйста, пополните сначала',
      email: 'Email',
      cpUsage: 'Использование кредитов CP',
      advancedModelUsage: 'Использование продвинутой модели',
      basicModelUsage: 'Использование базовой модели',
      cannotGetUsage: 'Не удается подключиться',
      cursorHistoryDownload: 'Скачать исторические версии Cursor',
      cursorDbError: 'База данных Cursor не существует или данные не найдены',
      cursorNetworkError: 'Ошибка подключения к сети, проверьте вашу сеть',
      cursorDataError: 'Cursor вернул данные в неправильном формате',
      cursorUnknownError: 'Неизвестная ошибка Cursor, перезапустите программу',
      cursorProUnlimitedTip:
        'Cursor latest policy changed to Pro and Pro trial users can call models unlimited',
    },
    login: {
      title: 'Вход',
      emailPlaceholder: 'Введите адрес электронной почты',
      passwordPlaceholder: 'Введите пароль',
      smsCodePlaceholder: 'Введите код подтверждения',
      sendCode: 'Отправить код',
      resendCode: 'Отправить повторно через {seconds}с',
      loginButton: 'Вход',
      registerButton: 'Регистрация',
      forgotPassword: 'Забыли пароль?',
      emailError: 'Введите действительный адрес электронной почты',
      passwordError: 'Введите пароль',
      loginSuccess: 'Вход успешен',
      loginFailed: 'Вход не удался',
      noAccount: 'Нет аккаунта?',
      register: 'Зарегистрироваться сейчас',
      hasAccount: 'Уже есть аккаунт? Войти',
      userExists: 'Этот email уже зарегистрирован, переключение в режим входа',
      userNotExists: 'Этот email не зарегистрирован, пожалуйста, зарегистрируйтесь',
      emailInvalid: 'Введите действительный адрес электронной почты',
      emailUnsupported: 'Этот email-домен не поддерживается',
      passwordInvalid:
        'Password must contain at least 8 characters, including uppercase, lowercase letters and numbers',
    },
    settings: {
      activation: 'Активация',
      activationCode: 'Код активации',
      activate: 'Активировать',
      changePassword: 'Изменить пароль',
      currentPassword: 'Текущий пароль',
      newPassword: 'Новый пароль',
      confirmPassword: 'Подтвердить пароль',
      about: 'О программе',
      globalPreferences: 'Global Preferences',
      closeMethod: 'Close Method',
      operationMode: 'Operation Mode',
      simpleMode: 'Simple Mode',
      advancedMode: 'Advanced Mode',
      switchedToAdvanced: 'Switched to advanced mode',
      switchedToSimple: 'Switched to simple mode',
      settingsFailed: 'Settings failed',
      route: 'Route',
    },
    history: {
      title: 'История операций',
      filter: 'Фильтр',
      dateRange: 'Выберите период',
      type: 'Тип',
      detail: 'Подробности',
      time: 'Время',
      operator: 'Оператор',
      datePlaceholder: 'Выберите период',
      clearHistory: 'Очистить историю',
      clearSuccess: 'История успешно очищена',
      clearFailed: 'Не удалось очистить историю',
    },
    message: {
      pleaseInputActivationCode: 'Пожалуйста, введите код активации',
      activationSuccess: 'Активация успешна',
      activationFailed: 'Активация не удалась',
      pleaseInputEmail: 'Пожалуйста, введите email',
      addSuccess: 'Успешно добавлено',
      switchSuccess: 'Переключено на аккаунт: {email}',
      deleteSuccess: 'Успешно удалено',
      pleaseInputPassword: 'Пожалуйста, заполните все поля пароля',
      passwordNotMatch: 'Пароли не совпадают',
      passwordChangeSuccess: 'Пароль успешно изменен',
      passwordChangeFailed: 'Изменение пароля не удалось',
    },
    systemControl: {
      title: 'Управление системой',
      hookStatus: 'Состояние Hook',
      hookApplied: 'Применено',
      hookNotApplied: 'Не применено',
      applyHook: 'Применить Hook',
      restoreHook: 'Восстановить Hook',
      updateStatus: 'Статус автоматического обновления',
      updateDisabled: 'Отключено',
      updateEnabled: 'Включено',
      disableUpdate: 'Отключить обновление',
      restoreUpdate: 'Restaurar obnovení',
      clientStatus: 'Этап клиента',
      clientVerified: 'Проверено',
      clientUnverified: 'Не проверено',
      cursorPath: 'Путь Cursor: ',
      changePath: 'Изменить путь',
      systemNotification: 'Системное уведомление',
      authorized: 'Авторизовано',
      unauthorized: 'Не авторизовано',
      requestPermission: 'Запросить разрешение',
      messages: {
        disableUpdateSuccess: 'Автоматическое обновление успешно отключено',
        restoreUpdateSuccess: 'Автоматическое обновление успешно восстановлено',
        applyHookSuccess: 'Hook aplicado con éxito',
        restoreHookSuccess: 'Hook restaurado con éxito',
        cursorRunning: 'Cursor en ejecución, por favor guarde su trabajo antes de continuar!',
        forceKillConfirm: 'He guardado, forzar cierre',
        permissionGranted: 'Разрешение на уведомления предоставлено',
        permissionDenied:
          'Разрешение отклонено, пожалуйста, включите уведомления вручную в настройках системы',
      },
      history: {
        disableUpdate: 'Desactivar actualización automática',
        restoreUpdate: 'Restaurar actualización automática',
        applyHook: 'Aplicar hook',
        restoreHook: 'Restaurar hook',
      },
    },
    about: {
      title: 'О программе',
      version: 'Версия',
      appName: 'Cursor Pool',
      copyright: 'Copyright',
      license:
        'Open-sourced under MIT License. Copyright notice must be preserved when modified or distributed.',
      allRightsReserved: 'All Rights Reserved',
    },
    language: {
      title: 'Язык',
      switch: 'Изменить язык',
    },
    common: {
      logout: 'Выйти',
      copySuccess: 'Копирование успешно',
      copyFailed: 'Копирование не удалось',
      forceClose: 'Я сохранил, закройте принудительно',
      cursorRunning: 'Cursor в работе',
      cursorRunningMessage:
        'Обнаружен запущенный Cursor, пожалуйста, сохраните несохраненные изменения проекта, прежде чем продолжить!',
      closingCursor: 'Закрытие Cursor...',
      forgotPassword: 'Забыли пароль?',
      confirmRestart: 'Перезапустить сейчас',
      timeUnknown: 'Неизвестно',
      timeExpired: 'Истек срок',
      timeDays: 'дн.',
      timeHours: 'ч.',
      timeMinutes: 'мин.',
      statusUnknown: 'Неизвестно',
    },
    inbound: {
      title: 'Маршрут',
      selector: 'Выбор маршрута',
      switchSuccess: 'Переключено на маршрут: {name}',
      switchFailed: 'Не удалось переключить маршрут',
      restartNeeded:
        'Для применения новой конфигурации маршрута рекомендуется перезапустить приложение',
      defaultInbound: 'Маршрут по умолчанию',
      domestic: 'Внутренний маршрут',
      foreign: 'Международный маршрут',
    },
    closeType: {
      ask: 'Каждый раз спрашивать',
      minimize: 'Минимизировать',
      exit: 'Выйти из программы',
    },
    historyAccount: {
      title: 'История аккаунтов',
      email: 'Email',
      machineCode: 'Код устройства',
      advancedModelUsage: 'Использование продвинутой модели',
      actions: 'Действия',
      switchButton: 'Переключить',
      deleteButton: 'Удалить',
      refreshAll: 'Обновить все',
      clearHighUsage: 'Очистить аккаунты с высоким использованием',
      switchSuccess: 'Аккаунт успешно переключен',
      switchFailed: 'Не удалось переключить аккаунт',
      deleteSuccess: 'Успешно удалено',
      deleteFailed: 'Не удалось удалить',
      hookFailed: 'Не удалось внедрить, попробуйте сделать это вручную',
      refreshSuccess: 'Все аккаунты успешно обновлены',
      refreshPartial: 'Успешно обновлено {success}/{total} аккаунтов',
      refreshFailed: 'Не удалось обновить данные об использовании',
      noHighUsageAccounts: 'Нет аккаунтов с высоким использованием для очистки',
      clearHighUsageSuccess: 'Успешно очищено {count} аккаунтов с высоким использованием',
      clearHighUsageFailed: 'Не удалось очистить аккаунты с высоким использованием',
      loadFailed: 'Не удалось загрузить историю аккаунтов',
    },
    notification: {
      testTitle: 'Тест уведомления',
      testBody: 'Поздравляем! Функция уведомлений успешно включена.',
    },
    network: {
      retrying: 'Повторная попытка {endpoint}, попытка {attempt}/{max}',
      refreshingInbound: 'Обновление маршрута...',
      inboundRefreshed: 'Маршрут обновлен',
      requestFailed: 'Запрос не удался, повторная попытка',
      connectionRestored: 'Соединение восстановлено',
    },
  },
  'es-AR': {
    appName: 'Cursor Pool',
    menu: {
      dashboard: 'Panel',
      historyAccount: 'Historial de cuentas',
      history: 'Historial',
      settings: 'Configuración',
    },
    dashboard: {
      deviceInfo: 'Información del dispositivo',
      machineCode: 'Código de máquina',
      currentAccount: 'Cuenta actual',
      expiryDate: 'Fecha de vencimiento',
      usageStats: 'Estadísticas de uso',
      advancedModel: 'Uso del modelo avanzado',
      normalModel: 'Uso del modelo normal',
      quickActions: 'Acciones rápidas',
      changeAccount: 'Cambiar cuenta',
      changeMachineCode: 'Cambiar código de máquina',
      quickChange: 'Cambio rápido (cuenta + código de máquina)',
      changeSuccess: 'Cambio exitoso',
      changeFailed: 'Cambio fallido',
      accountChangeSuccess: 'Cambio de cuenta exitoso',
      accountChangeFailed: 'Cambio de cuenta fallido',
      machineChangeSuccess: 'Cambio de máquina exitoso',
      machineChangeFailed: 'Cambio de máquina fallido',
      userInfo: 'Información del usuario',
      level: 'Nivel de miembro',
      username: 'Nombre de usuario',
      expireTime: 'Fecha de vencimiento',
      dailyUsage: 'Uso diario',
      cursorInfo: 'Información del cursor',
      cursorAccount: 'Cuenta del cursor',
      cursorUsage: 'Uso del cursor',
      notLoggedIn: 'No conectado',
      unlimited: 'Ilimitado',
      serverNotConnected: 'No conectado',
      codeUnused: 'No utilizado',
      codeExpired: 'Vencido',
      codeRefunded: 'Reembolsado',
      codeEnded: 'Finalizado',
      memberLevel: {
        1: 'Código',
        2: 'Programador',
        3: 'Ingeniero',
        4: 'Arquitecto',
        5: 'Director técnico',
      },
      newVersionAvailable: 'Nueva versión encontrada',
      currentVersion: 'Versión actual',
      newVersion: 'Nueva versión',
      later: 'Actualizar más tarde',
      downloadNow: 'Descargar ahora',
      ccStatus: 'Estado CC',
      registerTime: 'Hora de registro',
      insufficientCredits: 'Créditos insuficientes, por favor recargue primero',
      email: 'Email',
      cpUsage: 'Uso de créditos CP',
      advancedModelUsage: 'Uso de modelo avanzado',
      basicModelUsage: 'Uso de modelo básico',
      cannotGetUsage: 'No se puede conectar',
      cursorHistoryDownload: 'Descargar versiones históricas de Cursor',
      cursorDbError: 'La base de datos de Cursor no existe o no se encontraron datos',
      cursorNetworkError: 'Error de conexión de red, compruebe su red',
      cursorDataError: 'Formato de datos anormal devuelto por Cursor',
      cursorUnknownError: 'Error desconocido de Cursor, reinicie el software',
      cursorProUnlimitedTip:
        'Cursor latest policy changed to Pro and Pro trial users can call models unlimited',
    },
    login: {
      title: 'Iniciar sesión',
      emailPlaceholder: 'Ingrese su correo electrónico',
      passwordPlaceholder: 'Ingrese su contraseña',
      smsCodePlaceholder: 'Ingrese el código de verificación',
      sendCode: 'Enviar código',
      resendCode: 'Reenviar en {seconds}s',
      loginButton: 'Iniciar sesión',
      registerButton: 'Registrarse',
      forgotPassword: '¿Olvidó su contraseña?',
      emailError: 'Ingrese una dirección de correo electrónico válida',
      passwordError: 'Ingrese su contraseña',
      loginSuccess: 'Iniciar sesión exitoso',
      loginFailed: 'Iniciar sesión fallido',
      noAccount: '¿No tienes cuenta?',
      register: 'Regístrate ahora',
      hasAccount: '¿Ya tienes cuenta? Iniciar sesión',
      userExists: 'Este correo ya está registrado, cambio a modo de inicio de sesión',
      userNotExists: 'Este correo no está registrado, por favor regístrese primero',
      emailInvalid: 'Ingrese una dirección de correo electrónico válida',
      emailUnsupported: 'Este dominio de correo electrónico no es compatible',
      passwordInvalid:
        'Password must contain at least 8 characters, including uppercase, lowercase letters and numbers',
    },
    settings: {
      activation: 'Activación',
      activationCode: 'Código de activación',
      activate: 'Activar',
      changePassword: 'Cambiar contraseña',
      currentPassword: 'Contraseña actual',
      newPassword: 'Nueva contraseña',
      confirmPassword: 'Confirmar contraseña',
      about: 'Acerca de',
      globalPreferences: 'Preferencias globales',
      closeMethod: 'Método de cierre',
      operationMode: 'Modo de operación',
      simpleMode: 'Modo simple',
      advancedMode: 'Modo avanzado',
      switchedToAdvanced: 'Cambiado a modo avanzado',
      switchedToSimple: 'Cambiado a modo simple',
      settingsFailed: 'Configuración fallida',
      route: 'Ruta',
    },
    history: {
      title: 'Historial de operaciones',
      filter: 'Filtrar',
      dateRange: 'Seleccionar rango de fechas',
      type: 'Tipo',
      detail: 'Detalle',
      time: 'Hora',
      operator: 'Operador',
      datePlaceholder: 'Seleccionar rango de fechas',
      clearHistory: 'Limpiar historial',
      clearSuccess: 'Historico borrado exitosamente',
      clearFailed: 'Error al borrar historial',
    },
    message: {
      pleaseInputActivationCode: 'Por favor ingrese el código de activación',
      activationSuccess: 'Activation exitosa',
      activationFailed: 'Activation fallida',
      pleaseInputEmail: 'Por favor ingrese el correo electrónico',
      addSuccess: 'Agregado exitosamente',
      switchSuccess: 'Cambio de cuenta exitoso: {email}',
      deleteSuccess: 'Eliminado exitosamente',
      pleaseInputPassword: 'Por favor complete todos los campos de contraseña',
      passwordNotMatch: 'Las contraseñas no coinciden',
      passwordChangeSuccess: 'Contraseña cambiada exitosamente',
      passwordChangeFailed: 'Cambio de contraseña fallido',
    },
    systemControl: {
      title: 'Control del sistema',
      hookStatus: 'Estado del Hook',
      hookApplied: 'Aplicado',
      hookNotApplied: 'No aplicado',
      applyHook: 'Aplicar Hook',
      restoreHook: 'Restaurar Hook',
      updateStatus: 'Estado de actualización automática',
      updateDisabled: 'Desactivado',
      updateEnabled: 'Activado',
      disableUpdate: 'Desactivar actualización',
      restoreUpdate: 'Restaurar actualización',
      clientStatus: 'État du client',
      clientVerified: 'Vérifié',
      clientUnverified: 'Non vérifié',
      cursorPath: 'Ruta de Cursor: ',
      changePath: 'Cambiar ruta',
      systemNotification: 'Notificación del sistema',
      authorized: 'Autorisé',
      unauthorized: 'Non autorisé',
      requestPermission: 'Demander une autorisation',
      messages: {
        disableUpdateSuccess: 'Actualización automática desactivada con éxito',
        restoreUpdateSuccess: 'Actualización automática restaurada con éxito',
        applyHookSuccess: 'Hook aplicado con éxito',
        restoreHookSuccess: 'Hook restaurado con éxito',
        cursorRunning: 'Cursor en ejecución, por favor guarde su trabajo antes de continuar!',
        forceKillConfirm: 'He guardado, forzar cierre',
        permissionGranted: 'Permiso de notificación concedido',
        permissionDenied:
          'Permiso denegado, active las notificaciones manualmente en la configuración del sistema',
      },
      history: {
        disableUpdate: 'Desactivar actualización automática',
        restoreUpdate: 'Restaurar actualización automática',
        applyHook: 'Aplicar hook',
        restoreHook: 'Restaurar hook',
      },
    },
    about: {
      title: 'Acerca de',
      version: 'Versión',
      appName: 'Cursor Pool',
      copyright: 'Copyright',
      license:
        'Open-sourced under MIT License. Copyright notice must be preserved when modified or distributed.',
      allRightsReserved: 'All Rights Reserved',
    },
    language: {
      title: 'Configuración de idioma',
      switch: 'Cambiar idioma',
    },
    common: {
      logout: 'Cerrar sesión',
      copySuccess: 'Copia exitosa',
      copyFailed: 'Copia fallida',
      forceClose: 'He guardado, forzar cierre',
      cursorRunning: 'Cursor en ejecución',
      cursorRunningMessage:
        'Se detectó que Cursor está en ejecución, ¡guarde el proyecto que no ha sido modificado antes de continuar!',
      closingCursor: 'Cerrando Cursor...',
      forgotPassword: '¿Olvidó su contraseña?',
      confirmRestart: 'Reiniciar ahora',
      timeUnknown: 'Desconocido',
      timeExpired: 'Expirado',
      timeDays: 'días',
      timeHours: 'horas',
      timeMinutes: 'minutos',
      statusUnknown: 'Estado desconocido',
    },
    inbound: {
      title: 'Ruta',
      selector: 'Seleccionar ruta',
      switchSuccess: 'Cambio de ruta realizado: {name}',
      switchFailed: 'Error al cambiar de ruta',
      restartNeeded:
        'Se recomienda reiniciar la aplicación para que la nueva configuración de ruta tenga efecto',
      defaultInbound: 'Ruta predeterminada',
      domestic: 'Ruta nacional',
      foreign: 'Ruta internacional',
    },
    closeType: {
      ask: 'Preguntar cada vez',
      minimize: 'Minimizar',
      exit: 'Salir del programa',
    },
    historyAccount: {
      title: 'Historial de cuentas',
      email: 'Email',
      machineCode: 'Código de máquina',
      advancedModelUsage: 'Uso del modelo avanzado',
      actions: 'Acciones',
      switchButton: 'Cambiar',
      deleteButton: 'Eliminar',
      refreshAll: 'Actualizar todo',
      clearHighUsage: 'Limpiar cuentas de alto uso',
      switchSuccess: 'Cuenta cambiada exitosamente',
      switchFailed: 'Fallo al cambiar cuenta',
      deleteSuccess: 'Eliminado exitosamente',
      deleteFailed: 'Fallo al eliminar',
      hookFailed: 'Inyección fallida, intente inyectar manualmente',
      refreshSuccess: 'Todas las cuentas actualizadas exitosamente',
      refreshPartial: 'Actualizadas exitosamente {success}/{total} cuentas',
      refreshFailed: 'Fallo al actualizar uso',
      noHighUsageAccounts: 'No hay cuentas de alto uso para limpiar',
      clearHighUsageSuccess: 'Limpiadas exitosamente {count} cuentas de alto uso',
      clearHighUsageFailed: 'Fallo al limpiar cuentas de alto uso',
      loadFailed: 'Fallo al cargar historial de cuentas',
    },
    notification: {
      testTitle: 'Prueba de Notificación',
      testBody: '¡Felicidades! La función de notificación se ha habilitado con éxito.',
    },
    network: {
      retrying: 'Reintentando {endpoint}, intento {attempt}/{max}',
      refreshingInbound: 'Actualizando ruta...',
      inboundRefreshed: 'Ruta actualizada',
      requestFailed: 'Solicitud fallida, reintentando conexión',
      connectionRestored: 'Conexión restaurada',
    },
  },
} as const

export type MessageSchema = (typeof messages)['zh-CN']
