[package]
name = "cursor-pool"
version = "1.8.13"
description = "Cursor Pool App"
authors = ["Cloxl"]
edition = "2021"
rust-version = "1.60"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "cursor_pool_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.0.0-alpha.12", features = [] }

[dependencies]
tauri = { version = "2.0.0-alpha.18", features = [ "macos-private-api", "tray-icon", "image-png", "devtools"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
reqwest = { version = "0.11", features = ["json", "multipart"] }
tokio = { version = "1.0", features = ["full"] }
rusqlite = { version = "0.30.0", features = ["bundled"] }
uuid = { version = "1.6.1", features = ["v4"] }
sha2 = "0.10.8"
rand = "0.8.5"
hex = "0.4.3"
sysinfo = "0.30"
dotenv = "0.15"
lazy_static = "1.4.0"
zip = "0.6"
md-5 = "0.10.6"
regex = "1.10.2"
tauri-plugin-os = "2"
tauri-plugin-shell = "2"
chrono = "0.4"
os_info = "3.7"
sys-info = "0.9"
tauri-plugin-positioner = { version = "2.0.0" }
http = "0.2"
tauri-plugin-dialog = "2"
tauri-plugin-process = "2"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json", "time"] }
tracing-appender = "0.2"
time = { version = "0.3", features = ["formatting", "macros"] }
tauri-plugin-macos-permissions = "2.2.0"

[target.'cfg(windows)'.dependencies]
windows = { version = "0.52", features = [
    "Win32_Foundation",
    "Win32_System_Threading",
    "Win32_UI_Shell",
    "Win32_UI_WindowsAndMessaging"
] }

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-single-instance = "2"
tauri-plugin-updater = "2"

[features]
custom-protocol = ["tauri/custom-protocol"]

[dev-dependencies]
cargo-husky = { version = "1.5.0", features = ["precommit-hook", "run-cargo-fmt", "run-cargo-clippy"] }
tempfile = "3.8.1"
